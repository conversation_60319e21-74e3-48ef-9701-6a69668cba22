/* Magic Menu - Component Styles */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-heading);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 44px; /* Touch-friendly size */
    min-width: 44px;
    text-align: center;
    line-height: 1;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover,
.btn-secondary:focus {
    background-color: var(--secondary-color);
    color: var(--white);
    text-decoration: none;
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-success:hover,
.btn-success:focus {
    background-color: #218838;
    border-color: #1e7e34;
    color: var(--white);
    text-decoration: none;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-block {
    width: 100%;
    display: flex;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Cards */
.card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.card-title {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.card-text {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Navigation */
.navbar {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    font-family: var(--font-heading);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-lg);
}

.navbar-nav a {
    color: var(--text-color);
    font-weight: var(--font-weight-semibold);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.navbar-nav a:hover,
.navbar-nav a:focus,
.navbar-nav a.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    text-decoration: none;
}

.navbar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-xs);
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-color);
    background-color: var(--white);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.1);
}

.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--error-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--success-color);
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-semibold);
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Loading states */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Badge */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: var(--white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-xl);
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-dialog {
    position: relative;
    width: auto;
    max-width: 500px;
    margin: var(--spacing-lg);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-xl);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    padding: 0;
    color: var(--text-muted);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}
