<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Manage your Magic Menu account, view order history, update profile information, and track your deliveries.">
    <meta name="keywords" content="account, profile, order history, Magic Menu account, user dashboard">
    <title>My Account - Magic Menu | Manage Your Profile</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="navbar-brand" aria-label="Magic Menu Home">
                    Magic Menu
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html" class="active">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart">
                            Cart (<span class="cart-count">0</span>)
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1>My Account</h1>
                <p>Manage your profile, view orders, and track deliveries</p>
            </div>
        </section>

        <!-- Account Content -->
        <section class="account-section section">
            <div class="container">
                <!-- Login/Register Forms (shown when not logged in) -->
                <div id="auth-forms" class="auth-forms">
                    <div class="auth-container">
                        <div class="auth-tabs">
                            <button class="auth-tab active" data-tab="login">Login</button>
                            <button class="auth-tab" data-tab="register">Register</button>
                        </div>
                        
                        <!-- Login Form -->
                        <div id="login-form" class="auth-form active">
                            <div class="form-container">
                                <h2>Welcome Back</h2>
                                <p>Sign in to your Magic Menu account</p>
                                
                                <form data-validate data-form-type="login">
                                    <div class="form-group">
                                        <label for="login-email" class="form-label">Email Address *</label>
                                        <input type="email" id="login-email" name="email" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="login-password" class="form-label">Password *</label>
                                        <div class="password-input">
                                            <input type="password" id="login-password" name="password" class="form-control" required>
                                            <button type="button" class="password-toggle" aria-label="Show password">👁️</button>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="remember">
                                            <span class="checkmark"></span>
                                            Remember me
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                                </form>
                                
                                <div class="auth-footer">
                                    <p><a href="#" class="forgot-password">Forgot your password?</a></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Register Form -->
                        <div id="register-form" class="auth-form">
                            <div class="form-container">
                                <h2>Create Account</h2>
                                <p>Join Magic Menu and start ordering authentic Nigerian cuisine</p>
                                
                                <form data-validate data-form-type="register">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="register-firstName" class="form-label">First Name *</label>
                                            <input type="text" id="register-firstName" name="firstName" class="form-control" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="register-lastName" class="form-label">Last Name *</label>
                                            <input type="text" id="register-lastName" name="lastName" class="form-control" required>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-email" class="form-label">Email Address *</label>
                                        <input type="email" id="register-email" name="email" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-phone" class="form-label">Phone Number *</label>
                                        <input type="tel" id="register-phone" name="phone" class="form-control" required placeholder="+234 ************">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-password" class="form-label">Password *</label>
                                        <div class="password-input">
                                            <input type="password" id="register-password" name="password" class="form-control" required minlength="8">
                                            <button type="button" class="password-toggle" aria-label="Show password">👁️</button>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="register-confirmPassword" class="form-label">Confirm Password *</label>
                                        <input type="password" id="register-confirmPassword" name="confirmPassword" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="terms" required>
                                            <span class="checkmark"></span>
                                            I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a> *
                                        </label>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="newsletter">
                                            <span class="checkmark"></span>
                                            Subscribe to our newsletter for special offers
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Dashboard (shown when logged in) -->
                <div id="account-dashboard" class="account-container" style="display: none;">
                    <!-- Account Sidebar -->
                    <div class="account-sidebar">
                        <div class="user-info">
                            <h3 id="user-name">John Doe</h3>
                            <p id="user-email"><EMAIL></p>
                        </div>
                        
                        <nav class="account-navigation">
                            <ul class="account-nav">
                                <li><a href="#profile" class="account-nav-link active" data-section="profile">Profile</a></li>
                                <li><a href="#orders" class="account-nav-link" data-section="orders">Order History</a></li>
                                <li><a href="#addresses" class="account-nav-link" data-section="addresses">Addresses</a></li>
                                <li><a href="#preferences" class="account-nav-link" data-section="preferences">Preferences</a></li>
                                <li><a href="#" class="account-nav-link" id="logout-btn">Logout</a></li>
                            </ul>
                        </nav>
                    </div>
                    
                    <!-- Account Content -->
                    <div class="account-content">
                        <!-- Profile Section -->
                        <div id="profile-section" class="account-section active">
                            <h2>Profile Information</h2>
                            <div class="profile-info">
                                <div class="info-item">
                                    <span class="info-label">Name:</span>
                                    <span class="info-value" id="profile-name">John Doe</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Email:</span>
                                    <span class="info-value" id="profile-email"><EMAIL></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Phone:</span>
                                    <span class="info-value" id="profile-phone">+234 ************</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Member Since:</span>
                                    <span class="info-value" id="profile-member-since">January 2024</span>
                                </div>
                            </div>
                            <button class="btn btn-primary">Edit Profile</button>
                        </div>
                        
                        <!-- Orders Section -->
                        <div id="orders-section" class="account-section">
                            <h2>Order History</h2>
                            <div id="order-history">
                                <!-- Order history will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Addresses Section -->
                        <div id="addresses-section" class="account-section">
                            <h2>Saved Addresses</h2>
                            <p>Manage your delivery addresses for faster checkout.</p>
                            <button class="btn btn-primary">Add New Address</button>
                        </div>
                        
                        <!-- Preferences Section -->
                        <div id="preferences-section" class="account-section">
                            <h2>Preferences</h2>
                            <div class="preferences-form">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="email-notifications" checked>
                                        <span class="checkmark"></span>
                                        Email notifications for order updates
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="sms-notifications" checked>
                                        <span class="checkmark"></span>
                                        SMS notifications for delivery updates
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="newsletter">
                                        <span class="checkmark"></span>
                                        Newsletter with special offers and new menu items
                                    </label>
                                </div>
                                <button class="btn btn-primary">Save Preferences</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+2348012345678">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/forms.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Account page functionality
        document.addEventListener('DOMContentLoaded', () => {
            checkAuthStatus();
            setupAccountNavigation();
            setupAuthTabs();
        });

        function checkAuthStatus() {
            const user = Utils.storage.get('user');
            const authForms = document.getElementById('auth-forms');
            const accountDashboard = document.getElementById('account-dashboard');
            
            if (user && user.loggedIn) {
                // User is logged in, show dashboard
                authForms.style.display = 'none';
                accountDashboard.style.display = 'block';
                loadUserData(user);
            } else {
                // User is not logged in, show auth forms
                authForms.style.display = 'block';
                accountDashboard.style.display = 'none';
            }
        }

        function loadUserData(user) {
            document.getElementById('user-name').textContent = user.name;
            document.getElementById('user-email').textContent = user.email;
            document.getElementById('profile-name').textContent = user.name;
            document.getElementById('profile-email').textContent = user.email;
            document.getElementById('profile-phone').textContent = user.phone || 'Not provided';
            
            if (user.registrationTime) {
                const memberSince = Utils.formatDate(user.registrationTime, { year: 'numeric', month: 'long' });
                document.getElementById('profile-member-since').textContent = memberSince;
            }
            
            loadOrderHistory();
        }

        function loadOrderHistory() {
            const lastOrder = Utils.storage.get('lastOrder');
            const orderHistoryContainer = document.getElementById('order-history');
            
            if (lastOrder) {
                orderHistoryContainer.innerHTML = `
                    <div class="order-history-item">
                        <div class="order-header">
                            <span class="order-id">#${lastOrder.orderNumber}</span>
                            <span class="order-status delivered">Delivered</span>
                        </div>
                        <div class="order-details">
                            <p><strong>Date:</strong> ${Utils.formatDate(lastOrder.orderTime)}</p>
                            <p><strong>Total:</strong> ${Utils.formatCurrency(lastOrder.totals.total)}</p>
                            <p><strong>Items:</strong> ${lastOrder.items.length} item(s)</p>
                        </div>
                        <div class="order-actions">
                            <button class="btn btn-secondary btn-sm">View Details</button>
                            <button class="btn btn-primary btn-sm">Reorder</button>
                        </div>
                    </div>
                `;
            } else {
                orderHistoryContainer.innerHTML = `
                    <div class="empty-state">
                        <p>You haven't placed any orders yet.</p>
                        <a href="menu.html" class="btn btn-primary">Browse Menu</a>
                    </div>
                `;
            }
        }

        function setupAccountNavigation() {
            const navLinks = document.querySelectorAll('.account-nav-link');
            const sections = document.querySelectorAll('.account-section');
            
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    if (link.id === 'logout-btn') {
                        logout();
                        return;
                    }
                    
                    const sectionId = link.dataset.section;
                    
                    // Update active nav link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    // Show corresponding section
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(`${sectionId}-section`).classList.add('active');
                });
            });
        }

        function setupAuthTabs() {
            const authTabs = document.querySelectorAll('.auth-tab');
            const authForms = document.querySelectorAll('.auth-form');
            
            authTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabType = tab.dataset.tab;
                    
                    // Update active tab
                    authTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Show corresponding form
                    authForms.forEach(form => {
                        form.classList.remove('active');
                    });
                    document.getElementById(`${tabType}-form`).classList.add('active');
                });
            });
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                Utils.storage.remove('user');
                MagicMenu.showToast('You have been logged out successfully.', 'info');
                checkAuthStatus();
            }
        }
    </script>
</body>
</html>
