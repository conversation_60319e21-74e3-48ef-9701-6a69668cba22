/**
 * Magic Menu - Form Validation and Handling
 * Handles form validation, submission, and user feedback
 */

const Forms = {
    // Initialize form handling
    init() {
        this.setupEventListeners();
        this.initializeValidation();
    },

    // Set up event listeners for forms
    setupEventListeners() {
        // Real-time validation on input
        document.addEventListener('input', (e) => {
            if (e.target.matches('.form-control')) {
                this.validateField(e.target);
            }
        });

        // Form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-validate]')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // Password visibility toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('.password-toggle')) {
                this.togglePasswordVisibility(e.target);
            }
        });
    },

    // Initialize validation for existing forms
    initializeValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            this.setupFormValidation(form);
        });
    },

    // Set up validation for a specific form
    setupFormValidation(form) {
        const fields = form.querySelectorAll('.form-control');
        fields.forEach(field => {
            // Add validation attributes if not present
            if (field.hasAttribute('required') && !field.hasAttribute('aria-required')) {
                field.setAttribute('aria-required', 'true');
            }
        });
    },

    // Validate individual field
    validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        const fieldName = field.name || field.id;
        let isValid = true;
        let errorMessage = '';

        // Clear previous validation state
        this.clearFieldValidation(field);

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} is required.`;
        }
        // Email validation
        else if (fieldType === 'email' && value && !Utils.isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address.';
        }
        // Phone validation
        else if (fieldType === 'tel' && value && !Utils.isValidPhone(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid Nigerian phone number.';
        }
        // Password validation
        else if (fieldType === 'password' && value && value.length < 8) {
            isValid = false;
            errorMessage = 'Password must be at least 8 characters long.';
        }
        // Confirm password validation
        else if (fieldName === 'confirmPassword' || fieldName === 'password_confirm') {
            const passwordField = field.form.querySelector('input[type="password"]:not([name*="confirm"])');
            if (passwordField && value !== passwordField.value) {
                isValid = false;
                errorMessage = 'Passwords do not match.';
            }
        }
        // Custom validation patterns
        else if (field.hasAttribute('pattern') && value) {
            const pattern = new RegExp(field.getAttribute('pattern'));
            if (!pattern.test(value)) {
                isValid = false;
                errorMessage = field.getAttribute('data-error-message') || 'Please enter a valid value.';
            }
        }
        // Min/Max length validation
        else if (field.hasAttribute('minlength') && value.length < parseInt(field.getAttribute('minlength'))) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} must be at least ${field.getAttribute('minlength')} characters.`;
        }
        else if (field.hasAttribute('maxlength') && value.length > parseInt(field.getAttribute('maxlength'))) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} must be no more than ${field.getAttribute('maxlength')} characters.`;
        }

        // Apply validation state
        this.setFieldValidation(field, isValid, errorMessage);
        return isValid;
    },

    // Get field label for error messages
    getFieldLabel(field) {
        const label = field.form.querySelector(`label[for="${field.id}"]`);
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        
        // Fallback to placeholder or name
        return field.placeholder || field.name || 'This field';
    },

    // Clear field validation state
    clearFieldValidation(field) {
        field.classList.remove('is-valid', 'is-invalid');
        field.setAttribute('aria-invalid', 'false');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback, .valid-feedback');
        if (feedback) {
            feedback.remove();
        }
    },

    // Set field validation state
    setFieldValidation(field, isValid, message = '') {
        if (isValid) {
            field.classList.add('is-valid');
            field.classList.remove('is-invalid');
            field.setAttribute('aria-invalid', 'false');
            
            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'valid-feedback';
                feedback.textContent = message;
                field.parentNode.appendChild(feedback);
            }
        } else {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');
            field.setAttribute('aria-invalid', 'true');
            
            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                feedback.setAttribute('role', 'alert');
                field.parentNode.appendChild(feedback);
            }
        }
    },

    // Validate entire form
    validateForm(form) {
        const fields = form.querySelectorAll('.form-control');
        let isFormValid = true;

        fields.forEach(field => {
            const isFieldValid = this.validateField(field);
            if (!isFieldValid) {
                isFormValid = false;
            }
        });

        return isFormValid;
    },

    // Handle form submission
    async handleFormSubmit(form) {
        const isValid = this.validateForm(form);
        
        if (!isValid) {
            // Focus first invalid field
            const firstInvalidField = form.querySelector('.is-invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return;
        }

        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="loading"></span> Processing...';

        try {
            // Get form data
            const formData = this.getFormData(form);
            
            // Handle different form types
            const formType = form.dataset.formType || 'generic';
            await this.processForm(formType, formData, form);
            
        } catch (error) {
            console.error('Form submission error:', error);
            MagicMenu.showToast('An error occurred. Please try again.', 'error');
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    },

    // Get form data as object
    getFormData(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    },

    // Process different types of forms
    async processForm(formType, data, form) {
        switch (formType) {
            case 'contact':
                await this.processContactForm(data, form);
                break;
            case 'login':
                await this.processLoginForm(data, form);
                break;
            case 'register':
                await this.processRegisterForm(data, form);
                break;
            case 'checkout':
                await this.processCheckoutForm(data, form);
                break;
            default:
                console.log('Form submitted:', data);
                MagicMenu.showToast('Form submitted successfully!', 'success');
        }
    },

    // Process contact form
    async processContactForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(1000);
        
        MagicMenu.showToast('Thank you for your message! We\'ll get back to you soon.', 'success');
        form.reset();
    },

    // Process login form
    async processLoginForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(1500);
        
        // For demo purposes, accept any email/password
        if (data.email && data.password) {
            Utils.storage.set('user', {
                email: data.email,
                name: data.email.split('@')[0],
                loggedIn: true,
                loginTime: new Date().toISOString()
            });
            
            MagicMenu.showToast('Login successful! Welcome back.', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = 'account.html';
            }, 1000);
        }
    },

    // Process registration form
    async processRegisterForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(2000);
        
        Utils.storage.set('user', {
            email: data.email,
            name: `${data.firstName} ${data.lastName}`,
            phone: data.phone,
            loggedIn: true,
            registrationTime: new Date().toISOString()
        });
        
        MagicMenu.showToast('Registration successful! Welcome to Magic Menu.', 'success');
        
        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'account.html';
        }, 1000);
    },

    // Process checkout form
    async processCheckoutForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(3000);
        
        // Generate order number
        const orderNumber = 'MM' + Date.now().toString().slice(-6);
        
        // Store order data
        Utils.storage.set('lastOrder', {
            orderNumber: orderNumber,
            customerInfo: data,
            items: Cart.items,
            totals: Cart.getTotals(),
            orderTime: new Date().toISOString(),
            estimatedDelivery: new Date(Date.now() + 45 * 60 * 1000).toISOString() // 45 minutes
        });
        
        // Clear cart
        Cart.clearCart();
        
        MagicMenu.showToast('Order placed successfully!', 'success');
        
        // Redirect to confirmation page
        setTimeout(() => {
            window.location.href = 'confirmation.html';
        }, 1000);
    },

    // Toggle password visibility
    togglePasswordVisibility(button) {
        const passwordField = button.parentNode.querySelector('input[type="password"], input[type="text"]');
        if (!passwordField) return;

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            button.textContent = '🙈';
            button.setAttribute('aria-label', 'Hide password');
        } else {
            passwordField.type = 'password';
            button.textContent = '👁️';
            button.setAttribute('aria-label', 'Show password');
        }
    },

    // Simulate API call with delay
    simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};

// Initialize forms when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Forms.init();
});
